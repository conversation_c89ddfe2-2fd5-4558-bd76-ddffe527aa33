#include "sync_by_frwd.h"
#include <cassert>
#include <cmath>

namespace IQVideoProcessor::SignalProcessing {

using std::abs;
using std::ceil;

SyncByFrwd::SyncByFrwd(const TFloat* data, std::size_t effectiveLen)
  : data_(data), effectiveLen_(effectiveLen) {
  // Allow empty buffers: data_ may be null when effectiveLen_ == 0
  assert((effectiveLen_ == 0) || (data_ != nullptr));
}

bool SyncByFrwd::operator()(
  bool toEOF,
  int frontType,
  TFloat dblSearchedValue,
  TFloat dblFromPosition,
  TFloat dblElements,
  TFloat dblThresholdSize,
  TFloat dblThresholdTrigDelta,
  TFloat& frontPos,
  bool& frontFound) const
{
  frontFound = false;

  // Simplified parameter derivation - no averaging logic needed
  // aveSize is always 1, so no odd forcing or halfAveSize calculation
  const size_t thresholdSize = dblThresholdSize <= static_cast<TFloat>(1) ? 1 : static_cast<size_t>(dblThresholdSize + static_cast<TFloat>(0.5));

  TFloat dblEndPosition = dblFromPosition + dblElements;
  const size_t processingStartPos = static_cast<size_t>(dblFromPosition);

  size_t processingSize;
  if (toEOF) {
    processingSize = (processingStartPos < effectiveLen_) ? (effectiveLen_ - processingStartPos) : 0u;
    dblEndPosition = static_cast<TFloat>(effectiveLen_ - 1); // to EOF means to the last element
  } else if (dblElements <= static_cast<TFloat>(0)) {
    processingSize = 0u;
  } else {
    processingSize = static_cast<size_t>(ceil(dblEndPosition)) - processingStartPos;
    if (processingStartPos + processingSize > effectiveLen_) processingSize = effectiveLen_ - processingStartPos;
  }
  if (processingSize == 0 || processingSize <= thresholdSize) return true; // nothing to process, but not an error

  const TFloat* const data = data_ + processingStartPos; // base pointer for processing

  // No ring buffer initialization needed - we read directly from data
  
  // Threshold is used as-is (no scaling for averaging since aveSize=1)
  TFloat searchedDelta = abs(dblThresholdTrigDelta);
  
  // Process window setup
  size_t unprocessed = processingSize - thresholdSize;

  // Simplified main processing loop - no averaging case only
  size_t processed = 0;
  size_t prevValueIdx = 0;
  size_t nextValueIdx = thresholdSize;

  while (processed < unprocessed) {
    TFloat prevValue = data[prevValueIdx];
    TFloat nextValue = data[nextValueIdx];

    // Check for front detection
    TFloat delta = abs(nextValue - prevValue);
    if (delta >= searchedDelta) {
      bool validFrontAndCrossing = false;
      if (frontType > 0) {
        validFrontAndCrossing = (prevValue <= nextValue) && (prevValue <= dblSearchedValue && dblSearchedValue <= nextValue);
      } else if (frontType < 0) {
        validFrontAndCrossing = (prevValue >= nextValue) && (prevValue >= dblSearchedValue && dblSearchedValue >= nextValue);
      } else {
        const bool risingWithCrossing = (prevValue <= nextValue) && (prevValue <= dblSearchedValue && dblSearchedValue <= nextValue);
        const bool fallingWithCrossing = (prevValue >= nextValue) && (prevValue >= dblSearchedValue && dblSearchedValue >= nextValue);
        validFrontAndCrossing = risingWithCrossing || fallingWithCrossing;
      }

      if (validFrontAndCrossing) {
        TFloat valuePosition = findPreciseValueLocation(&data[prevValueIdx], thresholdSize, dblSearchedValue, frontType);
        TFloat calculatedPos = static_cast<TFloat>(processingStartPos + processed) + valuePosition;
        if (calculatedPos > dblFromPosition && calculatedPos <= dblEndPosition) {
          frontPos = calculatedPos;
          frontFound = true;
          return true;
        }
      }
    }

    ++prevValueIdx;
    ++nextValueIdx;
    ++processed;
  }

  return true; // executed, but no front found
}

TFloat SyncByFrwd::findPreciseValueLocation(const TFloat* data, size_t thresholdSize, TFloat searchedValue, int frontType) const {
  // Optimize for performance: use separate loops for different front types
  if (frontType > 0) {
    for (size_t i = 0; i < thresholdSize; ++i) {
      auto val1 = data[i];
      auto val2 = data[i + 1];

      if (val1 <= searchedValue && searchedValue <= val2) {
        auto fraction = interpolatePosition(val1, val2, searchedValue);
        return static_cast<TFloat>(i) + fraction;
      }
    }
  } else if (frontType < 0) {
    for (size_t i = 0; i < thresholdSize; ++i) {
      TFloat val1 = data[i];
      TFloat val2 = data[i + 1];

      if (val1 >= searchedValue && searchedValue >= val2) {
        TFloat fraction = interpolatePosition(val1, val2, searchedValue);
        return static_cast<TFloat>(i) + fraction;
      }
    }
  } else {
    for (size_t i = 0; i < thresholdSize; ++i) {
      TFloat val1 = data[i];
      TFloat val2 = data[i + 1];

      bool risingCrossing = (val1 <= searchedValue && searchedValue <= val2);
      bool fallingCrossing = (val1 >= searchedValue && searchedValue >= val2);

      if (risingCrossing || fallingCrossing) {
        TFloat fraction = interpolatePosition(val1, val2, searchedValue);
        return static_cast<TFloat>(i) + fraction;
      }
    }
  }

  return static_cast<TFloat>(thresholdSize) / static_cast<TFloat>(2); // Just in case, should never happen
}

inline TFloat SyncByFrwd::interpolatePosition(TFloat val1, TFloat val2, TFloat searchedValue) const {
  if (searchedValue == val1) return static_cast<TFloat>(0); // Start of interval
  if (searchedValue == val2) return static_cast<TFloat>(1); // End of interval
  return (searchedValue - val1) / (val2 - val1);
}


} // namespace IQVideoProcessor::SignalProcessing
