#pragma once

#include <cstddef>
#include "../../types.h"

namespace IQVideoProcessor::SignalProcessing {

/**
 * SyncPeakFrwd - Peak detection algorithm using two-step front synchronization
 *
 * This algorithm detects signal peaks by finding pairs of falling and rising fronts
 * within specified size constraints. It creates an instance of SyncByFrwd and performs
 * a two-step synchronization process:
 * 1. Find the first front (falling or rising based on input parameter)
 * 2. Find the corresponding opposite front starting from the first front's position
 * 3. Validate that the distance between fronts meets size requirements
 * 4. Continue searching if validation fails
 *
 * Key features:
 * - Two-step front detection with configurable direction
 * - Peak size validation (min/max constraints)
 * - Intelligent search range limiting for second front
 * - Precise interpolated positioning for both fronts
 * - Midpoint calculation between detected fronts
 */
class SyncPeakFrwd {
public:
  /**
   * Result structure containing peak detection information
   */
  struct PeakResult {
    TFloat first_sync_position{0};   // Position of first found front
    TFloat second_sync_position{0};  // Position of second found front
    TFloat middle_position{0};       // Calculated midpoint between the two fronts
    std::size_t peak_size{0};        // Sample count difference between the two fronts
    bool found{false};               // Indicates if a valid peak was detected
  };

  // Constructor: accepts same initialization parameters as SyncByFrwd
  explicit SyncPeakFrwd(const TFloat* data, std::size_t effectiveLen);

  /**
   * Peak detection operator
   * 
   * @param direction -1 for falling-then-rising sequence, +1 for rising-then-falling sequence
   * @param min_peak_size Minimum allowed distance between fronts in samples
   * @param max_peak_size Maximum allowed distance between fronts in samples
   * @param dblSearchedValue Value to search for in front crossing validation
   * @param dblFromPosition Starting position for search
   * @param dblElements Number of elements to process (0 means to EOF)
   * @param dblThresholdSize Threshold window size for front detection
   * @param dblThresholdTrigDelta Minimum delta required for front detection
   * @return PeakResult structure containing detection results
   */
  PeakResult operator()(int direction,
                        std::size_t min_peak_size,
                        std::size_t max_peak_size,
                        TFloat dblSearchedValue,
                        TFloat dblFromPosition,
                        TFloat dblElements,
                        TFloat dblThresholdSize,
                        TFloat dblThresholdTrigDelta) const;

private:
  const TFloat* data_{nullptr};
  std::size_t effectiveLen_{0};

  /**
   * Find a single front using SyncByFrwd
   * 
   * @param frontType Front type to search for (+1 rising, -1 falling, 0 bidirectional)
   * @param searchedValue Value to search for in crossing validation
   * @param fromPosition Starting position for search
   * @param elements Number of elements to process
   * @param thresholdSize Threshold window size
   * @param thresholdTrigDelta Minimum delta for front detection
   * @param foundPosition Output parameter for found position
   * @return true if front was found, false otherwise
   */
  bool findFront(int frontType,
                 TFloat searchedValue,
                 TFloat fromPosition,
                 TFloat elements,
                 TFloat thresholdSize,
                 TFloat thresholdTrigDelta,
                 TFloat& foundPosition) const;
};

} // namespace IQVideoProcessor::SignalProcessing
