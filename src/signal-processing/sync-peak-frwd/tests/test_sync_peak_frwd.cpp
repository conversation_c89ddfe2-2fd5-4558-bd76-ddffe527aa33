#include "../sync_peak_frwd.h"
#include <iostream>
#include <vector>
#include <stdexcept>
#include <cmath>

using namespace IQVideoProcessor::SignalProcessing;

// Helper function to create test data with a peak pattern
std::vector<float> makePeakData(size_t total, size_t peakStart, size_t peakWidth, 
                               float lowValue, float highValue, float peakValue) {
  std::vector<float> data(total, lowValue);
  
  // Create rising front
  data[peakStart] = highValue;
  
  // Fill peak region
  for (size_t i = peakStart + 1; i < peakStart + peakWidth && i < total; ++i) {
    data[i] = peakValue;
  }
  
  // Create falling front
  if (peakStart + peakWidth < total) {
    data[peakStart + peakWidth] = highValue;
    if (peakStart + peakWidth + 1 < total) {
      data[peakStart + peakWidth + 1] = lowValue;
    }
  }
  
  return data;
}

// Helper function to create step data (for single front testing)
std::vector<float> makeStep(size_t total, size_t stepPos, float lowValue, float highValue) {
  std::vector<float> data(total, lowValue);
  for (size_t i = stepPos; i < total; ++i) {
    data[i] = highValue;
  }
  return data;
}

void test_basic_peak_detection() {
  const size_t total = 1000;
  const size_t peakStart = 400;
  const size_t peakWidth = 100;

  // Create data with a clear peak: low -> high -> peak -> high -> low
  auto data = makePeakData(total, peakStart, peakWidth, -1.0f, 0.0f, 1.0f);

  SyncPeakFrwd<float> detector(data.data(), total);

  // Test rising-then-falling detection (direction = +1) - matches our data pattern
  auto result = detector(+1, 50, 150, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f);
  
  if (!result.found) {
    throw std::runtime_error("Basic peak detection failed - no peak found");
  }
  
  // Verify peak positions are reasonable
  if (result.first_sync_position < static_cast<float>(peakStart - 10) || 
      result.first_sync_position > static_cast<float>(peakStart + 10)) {
    throw std::runtime_error("First sync position inaccurate");
  }
  
  if (result.second_sync_position < static_cast<float>(peakStart + peakWidth - 10) ||
      result.second_sync_position > static_cast<float>(peakStart + peakWidth + 10)) {
    throw std::runtime_error("Second sync position inaccurate");
  }
  
  // Verify peak size is within expected range
  if (result.peak_size < 80 || result.peak_size > 120) {
    throw std::runtime_error("Peak size out of expected range");
  }
  
  // Verify middle position
  float expectedMiddle = (result.first_sync_position + result.second_sync_position) / 2.0f;
  if (std::abs(result.middle_position - expectedMiddle) > 0.1f) {
    throw std::runtime_error("Middle position calculation incorrect");
  }
  
  std::cout << "✓ basic peak detection" << std::endl;
}

void test_direction_parameters() {
  const size_t total = 800;

  // Test rising-then-falling (direction = +1) - normal peak pattern
  {
    auto data = makePeakData(total, 300, 80, -1.0f, 0.0f, 1.0f);
    SyncPeakFrwd<float> detector(data.data(), total);

    auto result = detector(+1, 50, 120, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f);
    if (!result.found) {
      throw std::runtime_error("Rising-then-falling detection failed");
    }
  }

  // Test falling-then-rising (direction = -1) - inverted peak pattern (valley)
  {
    auto data = makePeakData(total, 300, 80, 1.0f, 0.0f, -1.0f);
    SyncPeakFrwd<float> detector(data.data(), total);

    auto result = detector(-1, 50, 120, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f);
    if (!result.found) {
      throw std::runtime_error("Falling-then-rising detection failed");
    }
  }
  
  // Test invalid direction
  {
    auto data = makePeakData(total, 300, 80, -1.0f, 0.0f, 1.0f);
    SyncPeakFrwd<float> detector(data.data(), total);
    
    auto result = detector(0, 50, 120, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f);
    if (result.found) {
      throw std::runtime_error("Invalid direction should not find peaks");
    }
  }
  
  std::cout << "✓ direction parameters" << std::endl;
}

void test_size_validation() {
  const size_t total = 1000;
  
  // Create data with multiple peaks of different sizes
  std::vector<float> data(total, -1.0f);
  
  // Small peak (width 30)
  auto smallPeak = makePeakData(200, 50, 30, -1.0f, 0.0f, 1.0f);
  std::copy(smallPeak.begin(), smallPeak.end(), data.begin() + 100);
  
  // Large peak (width 150)
  auto largePeak = makePeakData(300, 100, 150, -1.0f, 0.0f, 1.0f);
  std::copy(largePeak.begin(), largePeak.end(), data.begin() + 600);
  
  SyncPeakFrwd<float> detector(data.data(), total);
  
  // Test finding only small peaks (min=20, max=50)
  {
    auto result = detector(+1, 20, 50, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f);
    if (!result.found) {
      throw std::runtime_error("Should find small peak");
    }
    if (result.peak_size > 50) {
      throw std::runtime_error("Found peak too large for size constraint");
    }
  }

  // Test finding only large peaks (min=100, max=200)
  {
    auto result = detector(+1, 100, 200, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f);
    if (!result.found) {
      throw std::runtime_error("Should find large peak");
    }
    if (result.peak_size < 100 || result.peak_size > 200) {
      throw std::runtime_error("Found peak outside size constraint");
    }
  }

  // Test invalid size constraints
  {
    auto result = detector(+1, 100, 50, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f); // min > max
    if (result.found) {
      throw std::runtime_error("Invalid size constraints should not find peaks");
    }
  }
  
  std::cout << "✓ size validation" << std::endl;
}

void test_edge_cases() {
  // Test empty data
  {
    SyncPeakFrwd<float> detector(nullptr, 0);
    auto result = detector(-1, 10, 100, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f);
    if (result.found) {
      throw std::runtime_error("Empty data should not find peaks");
    }
  }
  
  // Test single front only (no peak)
  {
    auto data = makeStep(500, 250, -1.0f, 1.0f);
    SyncPeakFrwd<float> detector(data.data(), 500);
    
    auto result = detector(+1, 50, 150, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f);
    if (result.found) {
      throw std::runtime_error("Single front should not be detected as peak");
    }
  }
  
  // Test very small data
  {
    std::vector<float> data = {-1.0f, 0.0f, 1.0f, 0.0f, -1.0f};
    SyncPeakFrwd<float> detector(data.data(), data.size());
    
    auto result = detector(+1, 1, 5, 0.0f, 0.0f, 0.0f, 1.0f, 0.5f);
    // Should handle gracefully without crashing
  }
  
  std::cout << "✓ edge cases" << std::endl;
}

void test_double_precision() {
  const size_t total = 500;
  std::vector<double> data(total, -1.0);
  
  // Create peak with double precision
  for (size_t i = 200; i < 250; ++i) {
    data[i] = 1.0;
  }
  data[200] = 0.0; // rising front
  data[250] = 0.0; // falling front
  
  SyncPeakFrwd<double> detector(data.data(), total);
  auto result = detector(+1, 30, 70, 0.0, 0.0, 0.0, 5.0, 0.5);
  
  if (!result.found) {
    throw std::runtime_error("Double precision peak detection failed");
  }
  
  std::cout << "✓ double precision instantiation" << std::endl;
}

int main() {
  try {
    test_basic_peak_detection();
    test_direction_parameters();
    test_size_validation();
    test_edge_cases();
    test_double_precision();
    
    std::cout << "\nAll SyncPeakFrwd tests passed!" << std::endl;
    return 0;
  } catch (const std::exception& e) {
    std::cerr << "Test failed: " << e.what() << std::endl;
    return 1;
  }
}
