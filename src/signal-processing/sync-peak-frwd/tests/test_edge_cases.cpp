#include "../sync_peak_frwd.h"
#include <iostream>
#include <vector>
#include <stdexcept>
#include <cmath>
#include <limits>

using namespace IQVideoProcessor::SignalProcessing;

void test_boundary_conditions() {
  const size_t total = 100;
  std::vector<float> data(total, 0.0f);
  
  // Peak at the very beginning
  data[0] = 0.5f;
  data[1] = 1.0f;
  data[2] = 1.0f;
  data[3] = 0.5f;
  
  // Peak near the end (but with some space for algorithm to work)
  data[90] = 0.5f;
  data[91] = 1.0f;
  data[92] = 1.0f;
  data[93] = 1.0f;
  data[94] = 0.5f;
  
  SyncPeakFrwd<float> detector(data.data(), total);
  
  // Test finding peak at beginning
  {
    auto result = detector(+1, 1, 5, 0.75f, 0.0f, 50.0f, 2.0f, 0.3f);
    if (!result.found) {
      throw std::runtime_error("Should find peak at beginning");
    }

    if (result.first_sync_position > 5.0f) {
      throw std::runtime_error("Beginning peak position incorrect");
    }
  }

  // Test finding peak at end
  {
    auto result = detector(+1, 1, 5, 0.75f, 80.0f, 0.0f, 2.0f, 0.3f);
    if (!result.found) {
      throw std::runtime_error("Should find peak at end");
    }

    if (result.first_sync_position < 85.0f) {
      throw std::runtime_error("End peak position incorrect");
    }
  }
  
  std::cout << "✓ boundary conditions" << std::endl;
}

void test_invalid_parameters() {
  const size_t total = 500;
  std::vector<float> data(total, 0.0f);
  
  // Add a valid peak for testing
  for (size_t i = 200; i <= 250; ++i) {
    data[i] = 1.0f;
  }
  data[199] = 0.5f;
  data[251] = 0.5f;
  
  SyncPeakFrwd<float> detector(data.data(), total);
  
  // Test min_peak_size > max_peak_size
  {
    auto result = detector(+1, 100, 50, 0.75f, 0.0f, 0.0f, 5.0f, 0.3f);
    if (result.found) {
      throw std::runtime_error("Invalid size constraints should not find peaks");
    }
  }

  // Test max_peak_size = 0
  {
    auto result = detector(+1, 0, 0, 0.75f, 0.0f, 0.0f, 5.0f, 0.3f);
    if (result.found) {
      throw std::runtime_error("Zero max size should not find peaks");
    }
  }
  
  // Test invalid direction values
  {
    auto result = detector(2, 40, 60, 0.75f, 0.0f, 0.0f, 5.0f, 0.3f);
    if (result.found) {
      throw std::runtime_error("Invalid direction should not find peaks");
    }
  }
  
  {
    auto result = detector(-2, 40, 60, 0.75f, 0.0f, 0.0f, 5.0f, 0.3f);
    if (result.found) {
      throw std::runtime_error("Invalid direction should not find peaks");
    }
  }
  
  std::cout << "✓ invalid parameters" << std::endl;
}

void test_extreme_values() {
  const size_t total = 200;
  std::vector<float> data(total);
  
  // Test with very large values
  std::fill(data.begin(), data.end(), 1000000.0f);
  data[99] = 1000001.0f;  // rising front
  for (size_t i = 100; i <= 120; ++i) {
    data[i] = 1000002.0f;
  }
  data[121] = 1000001.0f; // falling front
  
  SyncPeakFrwd<float> detector(data.data(), total);
  auto result = detector(+1, 15, 25, 1000001.5f, 0.0f, 0.0f, 3.0f, 0.5f);
  
  if (!result.found) {
    throw std::runtime_error("Should handle large values");
  }
  
  // Test with very small values
  std::fill(data.begin(), data.end(), 0.000001f);
  data[99] = 0.000002f;   // rising front
  for (size_t i = 100; i <= 120; ++i) {
    data[i] = 0.000003f;
  }
  data[121] = 0.000002f;  // falling front
  
  SyncPeakFrwd<float> detector2(data.data(), total);
  auto result2 = detector2(+1, 15, 25, 0.0000025f, 0.0f, 0.0f, 3.0f, 0.0000005f);
  
  if (!result2.found) {
    throw std::runtime_error("Should handle small values");
  }
  
  std::cout << "✓ extreme values" << std::endl;
}

void test_noisy_data() {
  const size_t total = 1000;
  std::vector<float> data(total);
  
  // Create noisy baseline
  for (size_t i = 0; i < total; ++i) {
    data[i] = 0.1f * std::sin(static_cast<float>(i) * 0.1f); // sine wave noise
  }
  
  // Add clear peak above noise
  data[399] = 0.8f;  // rising front
  for (size_t i = 400; i <= 450; ++i) {
    data[i] = 1.0f + 0.05f * std::sin(static_cast<float>(i) * 0.2f); // peak with small noise
  }
  data[451] = 0.8f;  // falling front
  
  SyncPeakFrwd<float> detector(data.data(), total);
  
  // Use higher threshold to ignore noise
  auto result = detector(+1, 40, 60, 0.9f, 0.0f, 0.0f, 5.0f, 0.6f);
  
  if (!result.found) {
    throw std::runtime_error("Should find peak above noise");
  }
  
  // Verify peak is in expected range
  if (result.first_sync_position < 395.0f || result.first_sync_position > 405.0f) {
    throw std::runtime_error("Noisy data peak position incorrect");
  }
  
  std::cout << "✓ noisy data" << std::endl;
}

void test_consecutive_peaks() {
  const size_t total = 500;
  std::vector<float> data(total, 0.0f);
  
  // Create consecutive peaks with shared boundaries
  // Peak 1: 100-120
  data[99] = 0.5f;
  for (size_t i = 100; i <= 120; ++i) {
    data[i] = 1.0f;
  }
  data[121] = 0.5f;
  
  // Peak 2: 125-145 (close to first peak)
  data[124] = 0.5f;
  for (size_t i = 125; i <= 145; ++i) {
    data[i] = 1.0f;
  }
  data[146] = 0.5f;
  
  SyncPeakFrwd<float> detector(data.data(), total);
  
  // Test finding first peak
  {
    auto result = detector(+1, 15, 25, 0.75f, 0.0f, 200.0f, 3.0f, 0.3f);
    if (!result.found) {
      throw std::runtime_error("Should find first consecutive peak");
    }

    // Should find first peak, not second
    if (result.first_sync_position > 110.0f) {
      throw std::runtime_error("Should find first peak in consecutive sequence");
    }
  }

  // Test finding second peak by starting search after first
  {
    auto result = detector(+1, 15, 25, 0.75f, 122.0f, 0.0f, 3.0f, 0.3f);
    if (!result.found) {
      throw std::runtime_error("Should find second consecutive peak");
    }

    // Should find second peak
    if (result.first_sync_position < 120.0f) {
      throw std::runtime_error("Should find second peak when starting after first");
    }
  }
  
  std::cout << "✓ consecutive peaks" << std::endl;
}

void test_minimal_data() {
  // Test with very small data arrays
  {
    std::vector<float> data = {0.0f, 1.0f, 0.0f};
    SyncPeakFrwd<float> detector(data.data(), data.size());
    
    auto result = detector(+1, 1, 3, 0.5f, 0.0f, 0.0f, 1.0f, 0.3f);
    // Should handle gracefully without crashing
  }
  
  {
    std::vector<float> data = {-1.0f, 0.0f, 1.0f, 0.0f, -1.0f};
    SyncPeakFrwd<float> detector(data.data(), data.size());
    
    auto result = detector(+1, 1, 4, 0.0f, 0.0f, 0.0f, 1.0f, 0.5f);
    if (!result.found) {
      // This is acceptable - minimal data might not have clear peaks
    }
  }
  
  std::cout << "✓ minimal data" << std::endl;
}

int main() {
  try {
    test_boundary_conditions();
    test_invalid_parameters();
    test_extreme_values();
    test_noisy_data();
    test_consecutive_peaks();
    test_minimal_data();
    
    std::cout << "\nAll edge case tests passed!" << std::endl;
    return 0;
  } catch (const std::exception& e) {
    std::cerr << "Test failed: " << e.what() << std::endl;
    return 1;
  }
}
