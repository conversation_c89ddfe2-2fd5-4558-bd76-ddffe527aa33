#include "../sync_peak_frwd.h"
#include <iostream>
#include <vector>
#include <stdexcept>
#include <cmath>

using namespace IQVideoProcessor::SignalProcessing;

// Helper to create complex peak patterns
std::vector<float> makeComplexPeakData(size_t total) {
  std::vector<float> data(total, 0.0f);
  
  // Multiple peaks with different characteristics
  // Peak 1: Small narrow peak at position 100-120
  for (size_t i = 100; i <= 120; ++i) {
    data[i] = 1.0f;
  }
  data[99] = 0.5f;   // rising front
  data[121] = 0.5f;  // falling front
  
  // Peak 2: Large wide peak at position 300-450
  for (size_t i = 300; i <= 450; ++i) {
    data[i] = 2.0f;
  }
  data[299] = 1.0f;  // rising front
  data[451] = 1.0f;  // falling front
  
  // Peak 3: Medium peak at position 600-680
  for (size_t i = 600; i <= 680; ++i) {
    data[i] = 1.5f;
  }
  data[599] = 0.75f; // rising front
  data[681] = 0.75f; // falling front
  
  return data;
}

void test_multiple_peaks_selection() {
  auto data = makeComplexPeakData(1000);
  SyncPeakFrwd<float> detector(data.data(), 1000);
  
  // Test finding first small peak (size 20-30)
  {
    auto result = detector(+1, 15, 35, 0.75f, 0.0f, 0.0f, 3.0f, 0.3f);
    if (!result.found) {
      throw std::runtime_error("Should find first small peak");
    }

    // Should find the first peak around position 100-120
    if (result.first_sync_position < 95.0f || result.first_sync_position > 105.0f) {
      throw std::runtime_error("First peak position incorrect");
    }

    if (result.peak_size < 15 || result.peak_size > 35) {
      throw std::runtime_error("First peak size out of range");
    }
  }

  // Test finding large peak (size 140-160)
  {
    auto result = detector(+1, 140, 160, 1.5f, 0.0f, 0.0f, 3.0f, 0.3f);
    if (!result.found) {
      throw std::runtime_error("Should find large peak");
    }

    // Should find the large peak around position 300-450
    if (result.first_sync_position < 295.0f || result.first_sync_position > 305.0f) {
      throw std::runtime_error("Large peak position incorrect");
    }
  }

  // Test finding medium peak (size 70-90)
  {
    auto result = detector(+1, 70, 90, 1.25f, 0.0f, 0.0f, 3.0f, 0.3f);
    if (!result.found) {
      throw std::runtime_error("Should find medium peak");
    }

    // Should find the medium peak around position 600-680
    if (result.first_sync_position < 595.0f || result.first_sync_position > 605.0f) {
      throw std::runtime_error("Medium peak position incorrect");
    }
  }
  
  std::cout << "✓ multiple peaks selection" << std::endl;
}

void test_search_range_limiting() {
  auto data = makeComplexPeakData(1000);
  SyncPeakFrwd<float> detector(data.data(), 1000);
  
  // Test limited search from position 200 (should skip first peak)
  {
    auto result = detector(+1, 70, 90, 1.25f, 200.0f, 500.0f, 3.0f, 0.3f);
    if (!result.found) {
      throw std::runtime_error("Should find peak in limited range");
    }

    // Should find medium peak, not the first small peak
    if (result.first_sync_position < 595.0f) {
      throw std::runtime_error("Should skip peaks before search range");
    }
  }

  // Test search with limited elements (should not reach third peak)
  {
    auto result = detector(+1, 140, 160, 1.5f, 0.0f, 500.0f, 3.0f, 0.3f);
    if (!result.found) {
      throw std::runtime_error("Should find peak in limited elements");
    }

    // Should find large peak, not the third peak
    if (result.first_sync_position > 500.0f) {
      throw std::runtime_error("Should not find peaks beyond element limit");
    }
  }
  
  std::cout << "✓ search range limiting" << std::endl;
}

void test_threshold_sensitivity() {
  const size_t total = 500;
  std::vector<float> data(total, 0.0f);

  // Create peak with very gradual transitions (delta = 0.2)
  for (size_t i = 200; i <= 250; ++i) {
    data[i] = 0.4f;
  }
  data[199] = 0.2f;  // small delta rising front (0.0 -> 0.2 -> 0.4, max delta = 0.2)
  data[251] = 0.2f;  // small delta falling front (0.4 -> 0.2 -> 0.0, max delta = 0.2)

  SyncPeakFrwd<float> detector(data.data(), total);

  // Test with high threshold (should not detect, threshold 0.5 > max delta 0.2)
  {
    auto result = detector(+1, 40, 60, 0.3f, 0.0f, 0.0f, 5.0f, 0.5f);
    if (result.found) {
      throw std::runtime_error("High threshold should not detect gradual peak");
    }
  }

  // Test with low threshold (should detect, threshold 0.1 < max delta 0.2)
  {
    auto result = detector(+1, 40, 60, 0.3f, 0.0f, 0.0f, 5.0f, 0.1f);
    if (!result.found) {
      throw std::runtime_error("Low threshold should detect gradual peak");
    }
  }

  std::cout << "✓ threshold sensitivity" << std::endl;
}

void test_interpolation_accuracy() {
  const size_t total = 200;
  std::vector<float> data(total, -1.0f);
  
  // Create peak with precise interpolation points
  data[99] = -0.5f;  // rising front start
  data[100] = 0.5f;  // rising front end (crosses 0.0 at 99.75)
  data[149] = 0.5f;  // falling front start  
  data[150] = -0.5f; // falling front end (crosses 0.0 at 149.25)
  
  for (size_t i = 101; i < 149; ++i) {
    data[i] = 1.0f;
  }
  
  SyncPeakFrwd<float> detector(data.data(), total);
  auto result = detector(+1, 45, 55, 0.0f, 0.0f, 0.0f, 2.0f, 0.3f);
  
  if (!result.found) {
    throw std::runtime_error("Interpolation test peak not found");
  }
  
  // Check interpolated positions (should be close to 99.75 and 149.25)
  if (std::abs(result.first_sync_position - 99.75f) > 0.5f) {
    throw std::runtime_error("First front interpolation inaccurate");
  }
  
  if (std::abs(result.second_sync_position - 149.25f) > 0.5f) {
    throw std::runtime_error("Second front interpolation inaccurate");
  }
  
  // Check middle position calculation
  float expectedMiddle = (99.75f + 149.25f) / 2.0f;
  if (std::abs(result.middle_position - expectedMiddle) > 0.1f) {
    throw std::runtime_error("Middle position interpolation inaccurate");
  }
  
  std::cout << "✓ interpolation accuracy" << std::endl;
}

void test_performance_scenarios() {
  // Test with large data set
  const size_t total = 50000;
  std::vector<float> data(total, 0.0f);
  
  // Create several peaks throughout the data
  for (int peak = 0; peak < 5; ++peak) {
    size_t peakStart = 5000 + peak * 8000;
    size_t peakEnd = peakStart + 100;
    
    if (peakEnd < total) {
      data[peakStart - 1] = 0.5f;  // rising front
      for (size_t i = peakStart; i <= peakEnd; ++i) {
        data[i] = 1.0f;
      }
      data[peakEnd + 1] = 0.5f;    // falling front
    }
  }
  
  SyncPeakFrwd<float> detector(data.data(), total);
  
  // Test finding first peak in large dataset
  auto result = detector(+1, 90, 110, 0.75f, 0.0f, 0.0f, 3.0f, 0.3f);
  
  if (!result.found) {
    throw std::runtime_error("Performance test should find peak in large dataset");
  }
  
  // Verify it found a reasonable peak
  if (result.peak_size < 90 || result.peak_size > 110) {
    throw std::runtime_error("Performance test peak size incorrect");
  }
  
  std::cout << "✓ performance scenarios" << std::endl;
}

int main() {
  try {
    test_multiple_peaks_selection();
    test_search_range_limiting();
    test_threshold_sensitivity();
    test_interpolation_accuracy();
    test_performance_scenarios();
    
    std::cout << "\nAll peak detection tests passed!" << std::endl;
    return 0;
  } catch (const std::exception& e) {
    std::cerr << "Test failed: " << e.what() << std::endl;
    return 1;
  }
}
