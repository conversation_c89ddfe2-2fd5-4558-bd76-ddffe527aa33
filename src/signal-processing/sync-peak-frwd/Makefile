# Makefile for SyncPeakFrwd module tests

CXX ?= g++
CXXFLAGS ?= -std=c++17 -Wall -Wextra -O2 -g -pedantic
INCLUDES = -I.. -I../.. -I../../chunk-processor -I../../iiq-stream -I../../stream-pipeline

# Source files
SYNC_PEAK_SOURCES = sync_peak_frwd.cpp ../sync-by-frwd/sync_by_frwd.cpp

# Test executables and their sources
BASIC_TEST_SOURCES = $(SYNC_PEAK_SOURCES) tests/test_sync_peak_frwd.cpp
BASIC_TEST_EXECUTABLE = sync_peak_frwd_tests

DETECTION_TEST_SOURCES = $(SYNC_PEAK_SOURCES) tests/test_peak_detection.cpp
DETECTION_TEST_EXECUTABLE = peak_detection_tests

EDGE_CASES_TEST_SOURCES = $(SYNC_PEAK_SOURCES) tests/test_edge_cases.cpp
EDGE_CASES_TEST_EXECUTABLE = edge_cases_tests

all: test

$(BASIC_TEST_EXECUTABLE): $(BASIC_TEST_SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^

$(DETECTION_TEST_EXECUTABLE): $(DETECTION_TEST_SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^

$(EDGE_CASES_TEST_EXECUTABLE): $(EDGE_CASES_TEST_SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^

build: $(BASIC_TEST_EXECUTABLE) $(DETECTION_TEST_EXECUTABLE) $(EDGE_CASES_TEST_EXECUTABLE)

test: $(BASIC_TEST_EXECUTABLE) $(DETECTION_TEST_EXECUTABLE) $(EDGE_CASES_TEST_EXECUTABLE)
	./$(BASIC_TEST_EXECUTABLE)
	./$(DETECTION_TEST_EXECUTABLE)
	./$(EDGE_CASES_TEST_EXECUTABLE)

clean:
	rm -f $(BASIC_TEST_EXECUTABLE) $(DETECTION_TEST_EXECUTABLE) $(EDGE_CASES_TEST_EXECUTABLE) tests/*.o *.o

.PHONY: all build test clean
