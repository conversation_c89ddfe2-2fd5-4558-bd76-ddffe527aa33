#include "sync_peak_frwd.h"
#include "../sync-by-frwd/sync_by_frwd.h"
#include <cassert>
#include <cmath>
#include <algorithm>

namespace IQVideoProcessor::SignalProcessing {

SyncPeakFrwd::SyncPeakFrwd(const TFloat* data, std::size_t effectiveLen)
  : data_(data), effectiveLen_(effectiveLen) {
  // Allow empty buffers: data_ may be null when effectiveLen_ == 0
  assert((effectiveLen_ == 0) || (data_ != nullptr));
}

typename SyncPeakFrwd::PeakResult SyncPeakFrwd<TFloat>::operator()(
  int direction,
  std::size_t min_peak_size,
  std::size_t max_peak_size,
  TFloat dblSearchedValue,
  TFloat dblFromPosition,
  TFloat dblElements,
  TFloat dblThresholdSize,
  TFloat dblThresholdTrigDelta) const
{
  PeakResult result;
  
  // Validate input parameters
  if (min_peak_size > max_peak_size || max_peak_size == 0) {
    return result; // Invalid size constraints
  }
  
  // Determine front types based on direction
  int firstFrontType, secondFrontType;
  if (direction == -1) {
    // Falling-then-rising sequence
    firstFrontType = -1;  // falling front
    secondFrontType = +1; // rising front
  } else if (direction == +1) {
    // Rising-then-falling sequence
    firstFrontType = +1;  // rising front
    secondFrontType = -1; // falling front
  } else {
    return result; // Invalid direction
  }
  
  TFloat searchPosition = dblFromPosition;
  TFloat remainingElements = dblElements;
  bool toEOF = (dblElements <= static_cast<TFloat>(0));
  
  while (true) {
    // Step 1: Find the first front
    TFloat firstFrontPos;
    bool firstFound = findFront(firstFrontType, dblSearchedValue, searchPosition, 
                               remainingElements, dblThresholdSize, dblThresholdTrigDelta, 
                               firstFrontPos);
    
    if (!firstFound) {
      break; // No more fronts found
    }
    
    // Step 2: Find the second front starting from first front position
    // Calculate search range for second front based on max_peak_size
    TFloat secondSearchStart = firstFrontPos;
    TFloat maxSearchRange = static_cast<TFloat>(max_peak_size) + dblThresholdSize;
    TFloat secondSearchElements;
    
    if (toEOF) {
      // Search to end of data, but limit by max_peak_size for efficiency
      TFloat maxEndPos = firstFrontPos + maxSearchRange;
      TFloat dataEndPos = static_cast<TFloat>(effectiveLen_ - 1);
      secondSearchElements = std::min(maxEndPos, dataEndPos) - secondSearchStart;
    } else {
      // Limit search by remaining elements and max_peak_size
      TFloat originalEndPos = dblFromPosition + dblElements;
      TFloat maxEndPos = firstFrontPos + maxSearchRange;
      TFloat effectiveEndPos = std::min(originalEndPos, maxEndPos);
      secondSearchElements = effectiveEndPos - secondSearchStart;
    }
    
    if (secondSearchElements <= static_cast<TFloat>(0)) {
      break; // No space to search for second front
    }
    
    TFloat secondFrontPos;
    bool secondFound = findFront(secondFrontType, dblSearchedValue, secondSearchStart,
                                secondSearchElements, dblThresholdSize, dblThresholdTrigDelta,
                                secondFrontPos);
    
    if (!secondFound) {
      // No second front found, continue searching from current position
      searchPosition = firstFrontPos + static_cast<TFloat>(1);
      if (!toEOF) {
        TFloat usedElements = searchPosition - dblFromPosition;
        remainingElements = dblElements - usedElements;
        if (remainingElements <= static_cast<TFloat>(0)) {
          break;
        }
      }
      continue;
    }
    
    // Step 3: Validate peak size
    std::size_t peakSize = static_cast<std::size_t>(std::abs(secondFrontPos - firstFrontPos));
    
    if (peakSize >= min_peak_size && peakSize <= max_peak_size) {
      // Valid peak found
      result.first_sync_position = firstFrontPos;
      result.second_sync_position = secondFrontPos;
      result.middle_position = (firstFrontPos + secondFrontPos) / static_cast<TFloat>(2);
      result.peak_size = peakSize;
      result.found = true;
      return result;
    }
    
    // Peak size validation failed, continue searching from second front position
    searchPosition = secondFrontPos + static_cast<TFloat>(1);
    if (!toEOF) {
      TFloat usedElements = searchPosition - dblFromPosition;
      remainingElements = dblElements - usedElements;
      if (remainingElements <= static_cast<TFloat>(0)) {
        break;
      }
    }
  }
  
  return result; // No valid peak found
}

template <typename TFloat>
bool SyncPeakFrwd<TFloat>::findFront(int frontType,
                                     TFloat searchedValue,
                                     TFloat fromPosition,
                                     TFloat elements,
                                     TFloat thresholdSize,
                                     TFloat thresholdTrigDelta,
                                     TFloat& foundPosition) const
{
  SyncByFrwd<TFloat> finder(data_, effectiveLen_);
  bool frontFound = false;
  bool toEOF = (elements <= static_cast<TFloat>(0));
  
  bool success = finder(toEOF, frontType, searchedValue, fromPosition, elements,
                       thresholdSize, thresholdTrigDelta, foundPosition, frontFound);
  
  return success && frontFound;
}

// Explicit template instantiations
template class SyncPeakFrwd<float>;
template class SyncPeakFrwd<double>;

} // namespace IQVideoProcessor::SignalProcessing
