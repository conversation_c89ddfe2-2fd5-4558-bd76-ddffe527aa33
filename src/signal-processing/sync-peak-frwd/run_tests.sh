#!/bin/bash

# Run all SyncPeakFrwd tests
# This script builds and executes all test suites for the SyncPeakFrwd module

set -e  # Exit on any error

echo "Building and running SyncPeakFrwd tests..."
echo "=========================================="

# Clean previous builds
echo "Cleaning previous builds..."
make clean

# Build all test executables
echo "Building test executables..."
make build

# Run all tests
echo ""
echo "Running basic SyncPeakFrwd tests..."
echo "-----------------------------------"
./sync_peak_frwd_tests

echo ""
echo "Running peak detection tests..."
echo "------------------------------"
./peak_detection_tests

echo ""
echo "Running edge cases tests..."
echo "--------------------------"
./edge_cases_tests

echo ""
echo "=========================================="
echo "All SyncPeakFrwd tests completed successfully!"
echo "=========================================="
