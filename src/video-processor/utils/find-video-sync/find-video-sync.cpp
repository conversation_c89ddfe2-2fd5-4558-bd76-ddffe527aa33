#include "./find-video-sync.h"


namespace IQVideoProcessor {

FindVideoSync::FindVideoSync(const TFloat* data, size_t dataSize, TFloat syncValue, TFloat thresholdTrigDelta, TFloat thresholdSize, TFloat maxPulseWidth, TFloat maxSyncDistance)
  : data_(data), dataSize_(dataSize), syncValue_(syncValue),
    thresholdTrigDelta_(thresholdTrigDelta), thresholdSize_(thresholdSize),
    maxPulseWidth_(maxPulseWidth), maxSyncDistance_(maxSyncDistance), syncByFrwd_(data, dataSize) {
}

[[nodiscard]] const FindVideoSync::SyncResult &FindVideoSync::getSyncResult() const {
  return syncResult_;
}

bool FindVideoSync::syncNext(const TFloat fromPosition) {
  if (syncByFrwdNext(-1, fromPosition, maxSyncDistance_)) {
    return false;
  }
  syncResult_.fallingFrontPosition = foundPosition_;
  if (syncByFrwdNext(+1, foundPosition_, maxPulseWidth_)) {
    return false;
  }
  syncResult_.risingFrontPosition = foundPosition_;
  syncResult_.centerPosition = (syncResult_.fallingFrontPosition + syncResult_.risingFrontPosition) / static_cast<TFloat>(2);
  syncResult_.pulseWidth = syncResult_.risingFrontPosition - syncResult_.fallingFrontPosition;
  return true;
}

bool FindVideoSync::syncByFrwdNext(const int frontType, const TFloat fromPosition, TFloat samples) {
  bool found = false;
  syncByFrwd_(
    false,
    frontType,
    syncValue_, // searched value
    fromPosition, // from position
    samples, // elements
    thresholdSize_, // threshold size
    thresholdTrigDelta_, // threshold trig delta
    foundPosition_, // output position
    found // output found flag
  );
  return found;
}

}
