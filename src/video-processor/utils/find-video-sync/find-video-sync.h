#pragma once

#include "../../../signal-processing/sync-by-frwd/sync_by_frwd.h"

namespace IQVideoProcessor {

class FindVideoSync {
public:
  FindVideoSync(const TFloat* data, size_t dataSize, TFloat syncValue, TFloat thresholdTrigDelta, TFloat thresholdSize, TFloat maxPulseWidth, TFloat maxSyncDistance);
  ~FindVideoSync() = default;

  struct SyncResult {
    TFloat fallingFrontPosition{0};
    TFloat risingFrontPosition{0};
    TFloat centerPosition{0};
    TFloat pulseWidth{0};
  };

  bool syncNext(TFloat fromPosition);
  [[nodiscard]] const SyncResult& getSyncResult() const;

private:
  const TFloat* data_;
  size_t dataSize_;
  TFloat syncValue_;
  TFloat thresholdTrigDelta_;
  TFloat thresholdSize_;
  TFloat foundPosition_{0};
  SyncResult syncResult_;
  SignalProcessing::SyncByFrwd syncByFrwd_;

  bool syncByFrwdNext(int frontType, TFloat fromPosition, TFloat samples);

  // Calculated values
  TFloat maxPulseWidth_; // The maximum amount of time a sync pulse can last, in samples
  TFloat maxSyncDistance_; // The maximum amount of time between falling sync pulses
};

}
