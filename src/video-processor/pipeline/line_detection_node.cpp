#include "./line_detection_node.h"
#include "../utils/find-video-sync/find-video-sync.h"
#include "../../types.h"
#include "../video_processor_configs.h"
#include "./devtools/data_exporter.hpp"
#include <algorithm>

namespace IQVideoProcessor::Pipeline {

std::vector<TFloat> frontGraphic;

LineDetectionNode::LineDetectionNode(SampleRateType sampleRate, size_t maxVideoLineSamples)
  : sampleRate_(sampleRate),
    maxVideoLineSamples_(maxVideoLineSamples)
  {
  _ds500kHz_.totalSamples = 0;
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment& segment) {
  if (!running()) return false;

  const auto effectiveStartIdx = segment.leftOverlapSamples;
  const auto effectiveSamples = segment.effectiveSamples;

  auto& filteredSegment = getAveFiltered500kHzSegment(segment);

  // Safety check: ensure we have valid data before proceeding
  if (filteredSegment.data.empty() || filteredSegment.totalSamples == 0) {
    // Log warning or handle empty data case
    return running(); // Continue running but skip processing this empty segment
  }

  // Additional safety check: ensure we have enough samples for effective processing
  if (effectiveStartIdx >= filteredSegment.totalSamples || effectiveSamples == 0) {
    return running(); // Skip processing if indices are invalid
  }

  const auto [minSampleValue, maxSampleValue] = getMinMax(&filteredSegment.data[effectiveStartIdx], effectiveSamples);
  const auto signalScale = maxSampleValue - minSampleValue;

  // SLOPE [EXPERIMENTAL]: 0.15 of the signal range per half of the average size samples
  TFloat pulseThresholdTrigDelta = signalScale * static_cast<TFloat>(0.15);
  TFloat pulseThresholdSize = static_cast<TFloat>(filteredSegment.aveSize) / static_cast<TFloat>(2);
  // SYNC VALUE [EXPERIMENTAL]: 0.18 of the signal range
  auto pulseSyncValue = minSampleValue + signalScale * static_cast<TFloat>(0.18); // 18% of the signal range

  auto maxPulseWidth = static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(32e-6); // 32 microseconds
  auto maxSyncDistance = static_cast<TFloat>(sampleRate_) / MIN_SAMPLES_PER_VIDEO_LINE;

  // Use safer data() method instead of &data[0] to avoid undefined behavior with empty vectors
  FindVideoSync findVideoSync(filteredSegment.data.data(), filteredSegment.totalSamples, pulseSyncValue, pulseThresholdTrigDelta, pulseThresholdSize, maxPulseWidth, maxSyncDistance);

  if (frontGraphic.size() != segment.totalSamples) frontGraphic.resize(segment.totalSamples);
  std::fill(frontGraphic.begin(), frontGraphic.end(), 0);

  TFloat fromPosition = 0;
  while (findVideoSync.syncNext(fromPosition)) {
    const auto& syncResult = findVideoSync.getSyncResult();
    fromPosition = syncResult.risingFrontPosition;
    // Mark the center position of the detected sync pulse in the front graphic
    const auto centerPosition = static_cast<size_t>(syncResult.centerPosition);
    frontGraphic[centerPosition] = pulseSyncValue;
  }

  DevTools::export_debug_data<TFloat>("LDN", "fronts", segment.segmentIndex, frontGraphic.data(), frontGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "original", segment.segmentIndex, segment.data.data(), segment.totalSamples);
  DevTools::export_debug_data<TFloat>("LDN", "ave500kHz", filteredSegment.segmentIndex, filteredSegment.data.data(), filteredSegment.totalSamples);

  return running();
}

const AveFilteredDemodulatedSegment& LineDetectionNode::getAveFiltered500kHzSegment(const DemodulatedSegment & segment) {
  // Safety check: ensure input segment has valid data
  if (segment.totalSamples == 0 || segment.data.empty()) {
    // Return empty segment but ensure it's properly initialized
    _ds500kHz_.data.clear();
    _ds500kHz_.totalSamples = 0;
    _ds500kHz_.effectiveSamples = 0;
    _ds500kHz_.leftOverlapSamples = 0;
    _ds500kHz_.aveSize = 0;
    _ds500kHz_.halfAveSize = 0;
    _ds500kHz_.segmentIndex = segment.segmentIndex;
    return _ds500kHz_;
  }

  if (_ds500kHz_.totalSamples == 0) {
    // Initialize the 500kHz segment with the first segment data
    _ds500kHz_.data.resize(segment.totalSamples);
    _ds500kHz_.totalSamples = segment.totalSamples;
    _ds500kHz_.effectiveSamples = segment.effectiveSamples;
    _ds500kHz_.leftOverlapSamples = segment.leftOverlapSamples;
    // Calculating the filter divider based on the sample rate
    _ds500kHz_.halfAveSize = (sampleRate_ / 500000) >> 1; // 500kHz
    _ds500kHz_.aveSize = (_ds500kHz_.halfAveSize << 1) + 1; // Force to odd: even -> +1, odd unchanged
  }
  _ds500kHz_.segmentIndex = segment.segmentIndex;

  const auto aveSize = _ds500kHz_.aveSize;
  const auto halfAveSize = _ds500kHz_.halfAveSize;

  // Additional safety check: ensure aveSize doesn't exceed available data
  const auto safeAveSize = std::min(aveSize, segment.totalSamples);

  TFloat summ = 0;
  for (size_t i = 0; i < safeAveSize; ++i) {
    summ += segment.data[i];
  }
  // Fill the start of the segment with the initial sum
  for (size_t i = 0; i <= halfAveSize; ++i) {
    _ds500kHz_.data[i] = summ; // Fill the first half with the initial sum
  }

  auto summLeaveIdx = 0;
  auto summEnterIdx = aveSize;
  auto summWriteIdx = halfAveSize + 1;

  // Sliding window to calculate the sum for the rest of the segment
  while (summEnterIdx < segment.totalSamples) {
    summ -= segment.data[summLeaveIdx++];
    summ += segment.data[summEnterIdx++];
    _ds500kHz_.data[summWriteIdx++] = summ;
  }
  // Fill the rest of the segment with the last sum
  while (summWriteIdx < segment.totalSamples) {
    _ds500kHz_.data[summWriteIdx++] = summ;
  }

  return _ds500kHz_;
}

std::tuple<TFloat, TFloat> LineDetectionNode::getMinMax(const TFloat* data, const size_t elements) {
  TFloat minVal = data[0];
  TFloat maxVal = data[0];
  for (size_t i = 1; i < elements; ++i) {
    if (data[i] < minVal) minVal = data[i];
    if (data[i] > maxVal) maxVal = data[i];
  }
  return std::make_tuple(minVal, maxVal);
}

} // namespace IQVideoProcessor::Pipeline
